from datetime import datetime
import os
from config import logger
from dotenv import load_dotenv

load_dotenv()
def generic_position_verification(broker, positions):
    try:
        # Retrieve expiry_day from .env file
        expiry_day = os.getenv("Expiry_Day").upper()  # Example: "MONDAY"     
             
        today_day = datetime.now().strftime("%A").upper()

       
        if broker.order_source == "fyers":
            if today_day == expiry_day:
                pass
            else:
                pass                
        elif broker.order_source == "symphony":
            client_id = os.getenv("Interactive_clientID")
            if today_day == expiry_day:
                pass
            else:
                pass                
        else:
            raise ValueError(f"Invalid order source: {broker.order_source}")
            
        return 
        
    except Exception as e:
        logger.error(f"Error fetching positions: {e}")
        raise
def handle_position_verification_failure(broker, positions):
    pass