from datetime import datetime
import os
from config import logger
from dotenv import load_dotenv
import json
from Generic_Squareoff import squareoff_positions
from Fyers import Fyers_save_final_pnl
import sys

load_dotenv()

def generic_position_verification(broker, positions, context=""):
    """
    Generic position verification function to validate BCS strategy positions.
    
    Args:
        broker: Broker context object
        positions: Current positions dictionary from get_positions()
        context: String to identify where this verification is called from (e.g., "execute_BCS", "deltaAction")
    
    Returns:
        bool: True if verification passes, False if verification fails
    """
    try:
        logger.info(f"Starting position verification - Context: {context}")
        
        # Retrieve expiry_day from .env file
        expiry_day = os.getenv("Expiry_Day", "THURSDAY").upper()  # Example: "TUESDAY"     
        today_day = datetime.now().strftime("%A").upper()
        is_expiry_day = (today_day == expiry_day)
        
        logger.info(f"Today: {today_day}, Expiry Day: {expiry_day}, Is Expiry Day: {is_expiry_day}")
        
        # Handle error cases in positions
        if isinstance(positions, dict) and "error" in positions:
            logger.error(f"Position data contains error: {positions['error']}")
            return False
        
        if not positions:
            logger.error("No position data received")
            return False
        
        # Count active positions and extract quantities
        active_positions = {}
        quantities = []
        position_details = []
        
        if broker.order_source == "fyers":
            if today_day == expiry_day:
                # Expiry day logic - expecting 6 positions
                # CE_BUY and PE_BUY are lists, CE_SHORT and PE_SHORT are dictionaries
                for position_type, position_data in positions.items():
                    if position_type in ["CE_BUY", "CE_SHORT", "PE_BUY", "PE_SHORT"]:
                        if position_type in ["CE_BUY", "PE_BUY"]:
                            # These are lists on expiry day
                            if isinstance(position_data, list):
                                for i, pos in enumerate(position_data):
                                    if pos.get("qty", 0) > 0:
                                        key = f"{position_type}_{i}"
                                        active_positions[key] = pos
                                        qty = abs(pos.get("qty", 0))
                                        quantities.append(qty)
                                        position_details.append(f"{pos.get('symbol', 'Unknown')}: {pos.get('side', 'Unknown')} {qty}")
                        else:
                            # CE_SHORT and PE_SHORT are dictionaries
                            if isinstance(position_data, dict) and position_data.get("qty", 0) > 0:
                                active_positions[position_type] = position_data
                                qty = abs(position_data.get("qty", 0))
                                quantities.append(qty)
                                position_details.append(f"{position_data.get('symbol', 'Unknown')}: {position_data.get('side', 'Unknown')} {qty}")
                
                expected_count = 6
                expected_qty_type = "half"
                
            else:
                # Normal day logic - expecting 4 positions
                # All are dictionaries
                for position_type, position_data in positions.items():
                    if position_type in ["CE_BUY", "CE_SHORT", "PE_BUY", "PE_SHORT"]:
                        if isinstance(position_data, dict) and position_data.get("qty", 0) > 0:
                            active_positions[position_type] = position_data
                            qty = abs(position_data.get("qty", 0))
                            quantities.append(qty)
                            position_details.append(f"{position_data.get('symbol', 'Unknown')}: {position_data.get('side', 'Unknown')} {qty}")
                
                expected_count = 4
                expected_qty_type = "full"
                
        elif broker.order_source == "symphony":
            client_id = os.getenv("Interactive_clientID")
            if today_day == expiry_day:
                # Symphony expiry day logic - expecting 6 positions
                for position_type, position_data in positions.items():
                    if position_type in ["CE_BUY", "CE_SHORT", "PE_BUY", "PE_SHORT"]:
                        if position_type in ["CE_BUY", "PE_BUY"]:
                            # These are lists on expiry day
                            if isinstance(position_data, list):
                                for i, pos in enumerate(position_data):
                                    if pos.get("qty", 0) > 0:
                                        key = f"{position_type}_{i}"
                                        active_positions[key] = pos
                                        qty = abs(pos.get("qty", 0))
                                        quantities.append(qty)
                                        position_details.append(f"{pos.get('symbol', 'Unknown')}: {pos.get('side', 'Unknown')} {qty}")
                        else:
                            # CE_SHORT and PE_SHORT are dictionaries
                            if isinstance(position_data, dict) and position_data.get("qty", 0) > 0:
                                active_positions[position_type] = position_data
                                qty = abs(position_data.get("qty", 0))
                                quantities.append(qty)
                                position_details.append(f"{position_data.get('symbol', 'Unknown')}: {position_data.get('side', 'Unknown')} {qty}")
                
                expected_count = 6
                expected_qty_type = "half"
                
            else:
                # Symphony normal day logic - expecting 4 positions
                for position_type, position_data in positions.items():
                    if position_type in ["CE_BUY", "CE_SHORT", "PE_BUY", "PE_SHORT"]:
                        if isinstance(position_data, dict) and position_data.get("qty", 0) > 0:
                            active_positions[position_type] = position_data
                            qty = abs(position_data.get("qty", 0))
                            quantities.append(qty)
                            position_details.append(f"{position_data.get('symbol', 'Unknown')}: {position_data.get('side', 'Unknown')} {qty}")
                
                expected_count = 4
                expected_qty_type = "full"
                
        else:
            logger.error(f"Invalid order source: {broker.order_source}")
            return False
        
        total_positions = len(active_positions)
        logger.info(f"Total active positions found: {total_positions}")
        logger.info(f"Position details: {position_details}")
        
        # Verification 1: Position Count Check
        if total_positions != expected_count:
            logger.error(f"Position count verification FAILED. Expected: {expected_count}, Found: {total_positions}")
            logger.error(f"Expected for {'expiry day' if is_expiry_day else 'normal day'}: {expected_count} positions")
            return False
        
        logger.info(f"Position count verification PASSED. Found {total_positions} positions as expected.")
        
        # Verification 2: Quantity Consistency Check
        if len(quantities) == 0:
            logger.error("No quantities found in positions")
            return False
        
        # Check if all quantities are equal
        if len(set(quantities)) != 1:
            logger.error(f"Quantity consistency verification FAILED. Found different quantities: {quantities}")
            return False
        
        base_qty = quantities[0]
        logger.info(f"Quantity consistency verification PASSED. All positions have quantity: {base_qty}")
        
        # Verification 3: Expected Quantity Type Check (Optional validation)
        global_qty = int(os.getenv("GLOBAL_QTY", "0"))
        resize_values_str = os.getenv("RESIZE_VALUES", "{}")
        
        try:
            resize_values = json.loads(resize_values_str)
            resize_factor = resize_values.get(expiry_day, {}).get(today_day, 1.0)
        except:
            resize_factor = 1.0
            logger.warning("Could not parse RESIZE_VALUES, using default factor 1.0")
        
        if is_expiry_day:
            # On expiry day, quantity should be half (resize factor applied)
            expected_base_qty = int(global_qty * resize_factor * 0.5)
        else:
            # On normal days, quantity should be full (resize factor applied)
            expected_base_qty = int(global_qty * resize_factor)
        
        if expected_base_qty > 0:
            if base_qty == expected_base_qty:
                logger.info(f"Quantity type verification PASSED. Expected: {expected_base_qty} ({expected_qty_type}), Found: {base_qty}")
            else:
                logger.warning(f"Quantity type verification WARNING. Expected: {expected_base_qty} ({expected_qty_type}), Found: {base_qty}")
                logger.warning(f"Global Qty: {global_qty}, Resize Factor: {resize_factor}")
        
        logger.info(f"Position verification COMPLETED SUCCESSFULLY - Context: {context}")
        return True
        
    except Exception as e:
        logger.error(f"Position verification ERROR - Context: {context}, Error: {e}")
        return False


def exit_trading(broker, all_positions, now, start_time, exit_time):
    squareoff_positions(broker, all_positions)  # Square off everything
    logger.info(f"Squared off all positions for today {now}. Time to save PNL and exit...")  
    Fyers_save_final_pnl.save_final_pnl(broker, start_time, exit_time) 
    logger.info(f"Today's {now}, PNL is saved. Exiting the script.")
    sys.exit()

